class Api::V1::BookingsController < Api::V1::BaseController
  before_action :set_booking, only: [ :show, :update, :cancel ]

  def index
    bookings = filtered_bookings

    @pagy, @bookings = pagy(
      bookings,
      items: pagination_validated_per_page,
      page: params[:page] || 1
    )
    set_pagination_headers(@pagy)

    render :index
  end

  def show
    render :show
  end

  def create
    raise Errors::InvalidInput, "Invalid Booking Type." unless Drive::BOOKING_TYPES.include?(create_params[:drive_type])

    # Create drive record
    @booking = dealership.drives.new(
      vehicle: find_vehicle(create_params[:vehicle_uuid]),
      drive_type: create_params[:drive_type],
      customer: find_or_create_customer(create_params[:customer_uuid], create_params[:customer_info]),
      expected_pickup_datetime: parse_date_time(create_params[:expected_pickup_datetime]),
      expected_return_datetime: parse_date_time(create_params[:expected_return_datetime]),
      sales_person: find_sales_person(create_params[:sales_person_uuid]),
      notes: create_params[:notes]
    )

    @booking.save!
    render :show, status: :created
  end

  def update
    @booking.update!(update_attrs)
    render :show
  end

  def cancel
    @booking.cancel_with_reason!(cancel_params[:cancel_reason])
    render :show
  end

  private

  def create_params
    params.expect(booking: [ :vehicle_uuid, :drive_type, :expected_pickup_datetime, :expected_return_datetime,
      :sales_person_uuid, :customer_uuid, :notes,
      customer_info: [ :first_name, :last_name, :age, :email, :phone_number, :gender, :address_line1,
        :address_line2, :suburb, :city, :state, :country, :postcode, :company_name,
        driver_license: [ :licence_number, :expiry_date, :issuing_state, :issuing_country, :full_name,
          :date_of_birth, :category, :issue_date, :front_image, :back_image ] ] ]
    )
  end

  def index_params
    params.permit(:status, :drive_type, :vehicle_uuid, :sales_person_uuid, :start_date, :end_date)
  end

  def cancel_params
    params.permit(:cancel_reason)
  end

  def update_params
    params.permit(:vehicle_uuid, :sales_person_uuid, :expected_pickup_datetime, :expected_return_datetime, :notes)
  end

  def filtered_bookings
    bookings = dealership.drives.bookings.includes(:vehicle, :sales_person, :customer, :dealership)
    bookings = bookings.pickup_between_dates(index_params[:start_date], index_params[:end_date]) if index_params[:start_date].present? || index_params[:end_date].present?
    bookings = bookings.by_vehicle(index_params[:vehicle_uuid]) if index_params[:vehicle_uuid].present?
    bookings = bookings.by_salesperson(index_params[:sales_person_uuid]) if index_params[:sales_person_uuid].present?
    bookings = bookings.filter_by_status(index_params[:status]) if index_params[:status].present?
    bookings = bookings.filter_by_drive_type(index_params[:drive_type]) if index_params[:drive_type].present?
    bookings.order(expected_pickup_datetime: :desc)
  end

  def set_booking
    @booking = dealership.drives.bookings.find_by!(uuid: params[:uuid])
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Booking not found"
  end

  def update_attrs
    update_attrs = {}
    update_attrs[:vehicle_id] = find_vehicle(update_params[:vehicle_uuid]).id if update_params[:vehicle_uuid].present?
    update_attrs[:notes] = update_params[:notes] if update_params[:notes].present?
    update_attrs[:sales_person_id] = find_sales_person(update_params[:sales_person_uuid]).id if update_params[:sales_person_uuid].present?
    update_attrs[:expected_pickup_datetime] = parse_date_time(update_params[:expected_pickup_datetime]) if update_params[:expected_pickup_datetime].present?
    update_attrs[:expected_return_datetime] = parse_date_time(update_params[:expected_return_datetime]) if update_params[:expected_return_datetime].present?
    update_attrs
  end
end
