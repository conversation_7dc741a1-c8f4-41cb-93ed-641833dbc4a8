class CustomerVehicle < ApplicationRecord
  include HasUuid
  include VehicleBase

  # Associations
  belongs_to :customer

  has_one :finance_details, class_name: "Vehicle::FinanceDetails", dependent: :destroy
  accepts_nested_attributes_for :finance_details
  has_one :options_fitted, class_name: "Vehicle::OptionsFitted", dependent: :destroy
  accepts_nested_attributes_for :options_fitted
  has_one :vehicle_condition, class_name: "Vehicle::VehicleCondition", dependent: :destroy
  accepts_nested_attributes_for :vehicle_condition
  has_one :vehicle_history, class_name: "Vehicle::VehicleHistory", dependent: :destroy
  accepts_nested_attributes_for :vehicle_history
  belongs_to :brand, optional: true
  has_many :appraisals, dependent: :destroy

  has_one_attached :main_photo
  has_one_attached :odometer_reading_photo

  # Validations
  validates :build_month, numericality: { greater_than: 0, less_than: 13 }, allow_nil: true
  validates :compliance_month, numericality: { greater_than: 0, less_than: 13 }, allow_nil: true
  validates :compliance_year, numericality: { greater_than: 1900 }, allow_nil: true
  validates :odometer_reading, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :number_of_doors, numericality: { greater_than: 0 }, allow_nil: true
  validates :number_of_seats, numericality: { greater_than: 0 }, allow_nil: true
  validates :engine_killowats, numericality: { greater_than: 0 }, allow_nil: true
  validates :wheel_size_front, numericality: { greater_than_or_equal_to: 13 }, allow_nil: true
  validates :wheel_size_rear, numericality: { greater_than_or_equal_to: 13 }, allow_nil: true

  # Enums
  enum :seat_type, {
    leather: 0,
    cloth: 1,
    mixed: 2
  }

  enum :fuel_type, {
    petrol: 0,
    diesel: 1,
    electric: 2,
    hybrid: 3,
    plugin_hybrid: 4,
    lpg: 5,
    other: 6
  }

  enum :driving_wheels, {
    fwd: 0,
    rwd: 1,
    awd: 2,
    four_wd: 3
  }

  enum :spare_wheel_type, {
    full_size: 0,
    space_saver: 1,
    run_flat: 2,
    repair_kit: 3,
    no_spare_wheel: 4
  }

  enum :transmission, {
    manual: 0,
    automatic: 1,
    cvt: 2,
    semi_automatic: 3,
    dual_clutch: 4
  }

  enum :body_type, {
    sedan: 0,
    hatchback: 1,
    wagon: 2,
    suv: 3,
    coupe: 4,
    convertible: 5,
    ute: 6,
    van: 7,
    truck: 8,
    unknown_body: 9
  }

  # Scopes
  scope :present, -> { where(is_vehicle_present: true) }
  scope :absent, -> { where(is_vehicle_present: false) }
end
