# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle1) { create(:vehicle, dealership: dealership) }
  let!(:customer1) { create(:customer, dealership: dealership) }

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/sold-status" do
    put "Update drive sold status" do
      tags "Drives"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"
      parameter name: :body, in: :body, schema: {
        type: :object,
        properties: {
          sold_status: {
                type: :string,
                enum: [ "sold", "unsold" ],
                description: "The sold status of the drive"
              }
        },
        required: [ "sold_status" ]
      }

      response "200", "Sold status updated successfully" do
        let(:test_drive) { create(:drive, drive_type: :test_drive, status: :completed, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person, sold_status: :unsold) }
        let(:uuid) { test_drive.uuid }

        context "when updating to sold" do
          let(:body) do
            {
              sold_status: "sold"
            }
          end

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Sold status updated successfully")
            expect(json.dig("data", "drive", "sold_status")).to eq("sold")
            expect(test_drive.reload.sold_status).to eq("sold")
          end
        end

        context "when updating to unsold" do
          let(:sold_drive) { create(:drive, drive_type: :enquiry, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person, sold_status: :sold) }
          let(:uuid) { sold_drive.uuid }
          let(:body) do
            {
              sold_status: "unsold"
            }
          end

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Sold status updated successfully")
            expect(json.dig("data", "drive", "sold_status")).to eq("unsold")
            expect(sold_drive.reload.sold_status).to eq("unsold")
          end
        end
      end

      response "404", "Drive not found" do
        let(:uuid) { "non-existent-uuid" }
        let(:body) do
          {
            sold_status: "sold"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to include("Drive not found")
        end
      end

      response "422", "Invalid sold status" do
        let(:test_drive) { create(:drive, drive_type: :test_drive, status: :completed, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }

        context "when sold_status is invalid" do
          let(:body) do
            {
              sold_status: "invalid_status"
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Invalid sold status")
          end
        end
      end

      response "422", "Sold status cannot be changed" do
        let(:test_drive) { create(:drive, drive_type: :test_drive, status: :in_progress, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:body) do
          {
            sold_status: "sold"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(422)
          expect(response.parsed_body.dig("status", "message")).to include("Sold status cannot be changed for this drive")
        end
      end

      response "422", "Sold status cannot be changed" do
        let(:test_drive) { create(:drive, drive_type: :loan, status: :in_progress, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:body) do
          {
            sold_status: "sold"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(422)
          expect(response.parsed_body.dig("status", "message")).to include("Sold status cannot be changed for this drive")
        end
      end

      response "401", "Unauthorized" do
        let(:test_drive) { create(:drive, drive_type: :test_drive, status: :completed, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:body) do
          {
            sold_status: "sold"
          }
        end

        context "when authorization header is missing" do
          let(:Authorization) { nil }

          run_test! do |response|
            expect(response.status).to eq(401)
          end
        end
      end
    end
  end
end
