# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle1) { create(:vehicle, dealership: dealership, last_known_odometer_km: 10000, rego: "ABC123") }
  let!(:customer1) { create(:customer, :with_driver_license_and_images, dealership: dealership) }
  let!(:driver_license) { create(:driver_license, holder: customer1) }
  let!(:trade_plate) { create(:trade_plate, dealership: dealership) }

  describe "PUT /api/v1/dealerships/:dealership_uuid/drives/:uuid/start" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/drives/#{drive.uuid}/start" }

    context "with valid draft drive" do
      let!(:drive) do
        create(:drive,
               dealership: dealership,
               vehicle: vehicle1,
               customer: customer1,
               sales_person: sales_person,
               drive_type: :test_drive,
               status: :draft,
               start_datetime: nil,
               end_datetime: nil,
               expected_return_datetime: 1.hour.from_now,
               start_odometer_reading: 10000)
      end

      context "without sales person accompanying" do
        let(:params) { { sales_person_accompanying: false } }

        it "starts the drive successfully" do
          expect {
            put url, params: params, headers: headers
          }.to change { drive.reload.status }.from("draft").to("in_progress")
            .and change { drive.reload.start_datetime }.from(nil)
            .and change { drive.reload.driver_license }.from(nil)

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Drive started successfully")

          drive_data = json.dig("data", "drive")
          expect(drive_data["status"]).to eq("in_progress")
          expect(drive_data["start_datetime"]).to be_present
          expect(drive_data["sales_person_accompanying"]).to be_nil
        end

        it "copies driver license data from customer" do
          put url, params: params, headers: headers

          drive.reload
          expect(drive.driver_license).to be_present
          expect(drive.driver_license.licence_number).to eq(customer1.driver_license.licence_number)
          expect(drive.driver_license.expiry_date).to eq(customer1.driver_license.expiry_date)
          expect(drive.driver_license.full_name).to eq(customer1.driver_license.full_name)
          expect(drive.driver_license.holder).to eq(drive)
        end
      end

      context "with sales person accompanying" do
        let(:params) { { sales_person_accompanying: true } }

        it "starts the drive and sets sales person accompanying" do
          put url, params: params, headers: headers

          expect(response).to have_http_status(:ok)
          drive.reload
          expect(drive.status).to eq("in_progress")
          expect(drive.sales_person_accompanying).to eq(user)
        end
      end
    end

    context "validation errors" do
      context "when drive type is not VEHICLE_OUT_DRIVE_TYPES" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :enquiry,
                 status: :draft)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Only test_drive, loan, self_loan drives can be started")
        end
      end

      context "when customer is null" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: nil,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Customer is required to start the drive")
        end
      end

      context "when vehicle rego is blank and trade plate is null" do
        let!(:vehicle_no_rego) { create(:vehicle, dealership: dealership, rego: nil) }
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle_no_rego,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 trade_plate: nil,
                 expected_return_datetime: 1.hour.from_now,
                 start_odometer_reading: 10000)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Trade plate is required when vehicle registration is blank")
        end
      end

      context "when status is not draft" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :in_progress)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Only draft drives can be started")
        end
      end

      context "when expected_return_datetime is null" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 expected_return_datetime: nil,
                 start_odometer_reading: 10000)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Expected return datetime is required")
        end
      end

      context "when expected_return_datetime is less than 5 minutes from now" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 expected_return_datetime: 2.minutes.from_now,
                 start_odometer_reading: 10000)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Expected return datetime must be at least 5 minutes from now")
        end
      end

      context "when start_odometer_reading is null" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 start_datetime: nil,
                 end_datetime: nil,
                 expected_return_datetime: 1.hour.from_now,
                 start_odometer_reading: nil)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Start odometer reading is required")
        end
      end

      context "when driver license is not complete" do
        let!(:customer1) { create(:customer, dealership: dealership) }
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 start_datetime: nil,
                 end_datetime: nil,
                 expected_return_datetime: 1.hour.from_now,
                 start_odometer_reading: 10000)
        end
        let!(:driver_license) { create(:driver_license, holder: customer1, date_of_birth: nil) }

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Driver license is not complete")
        end
      end

      context "when end_datetime is not null" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 expected_return_datetime: 1.hour.from_now,
                 start_odometer_reading: 10000,
                 start_datetime: nil,
                 end_datetime: Time.current)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("End datetime should not be set before starting the drive")
        end
      end

      context "when start_datetime is not null" do
        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 expected_return_datetime: 1.hour.from_now,
                 start_odometer_reading: 10000,
                 start_datetime: 1.hour.ago,
                 end_datetime: nil)
        end

        it "returns error" do
          put url, params: { sales_person_accompanying: false }, headers: headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Start datetime should not be set before starting the drive")
        end
      end
    end

    context "when drive not found" do
      let(:url) { "/api/v1/dealerships/#{dealership.uuid}/drives/non-existent-uuid/start" }

      it "returns 404" do
        put url, params: { sales_person_accompanying: false }, headers: headers

        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Drive not found")
      end
    end

    context "with invalid authentication" do
      let!(:drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }

      it "returns 401 without token" do
        put url, params: { sales_person_accompanying: false }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Rswag API Documentation
  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/start" do
    parameter name: "dealership_uuid", in: :path, type: :string, description: "Dealership UUID"
    parameter name: "uuid", in: :path, type: :string, description: "Drive UUID"

    put("Start a drive") do
      tags "Drives"
      description "Transitions a drive from draft to in_progress state. Validates all required conditions and copies driver license data from customer."
      operationId "startDrive"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token for authentication"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device ID for the request"

      parameter name: :body, in: :body, schema: {
        type: :object,
        properties: {
          sales_person_accompanying: {
            type: :boolean,
            description: "Whether sales person is accompanying the drive",
            example: true
          }
        }
      }

      response(200, "successful") do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: "Drive started successfully" }
                   },
                   required: [ "code", "message" ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     drive: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, example: "123e4567-e89b-12d3-a456-************" },
                         drive_type: { type: :string, example: "test_drive" },
                         status: { type: :string, example: "in_progress" },
                         start_datetime: { type: :string, format: "date-time", example: "2023-01-01T10:00:00Z" },
                         expected_return_datetime: { type: :string, format: "date-time", example: "2023-01-01T12:00:00Z" },
                         start_odometer_reading: { type: :integer, example: 10000 },
                         sales_person_accompanying: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string },
                             name: { type: :string }
                           }
                         },
                         driver_license: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string },
                             licence_number: { type: :string },
                             expiry_date: { type: :string, format: :date },
                             full_name: { type: :string }
                           }
                         }
                       }
                     }
                   }
                 }
               }

        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 start_datetime: nil,
                 status: :draft,
                 end_datetime: nil,
                 expected_return_datetime: 1.hour.from_now,
                 start_odometer_reading: 10000)
        end
        let(:uuid) { drive.uuid }
        let(:body) { { sales_person_accompanying: true } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("data", "drive", "status")).to eq("in_progress")
        end
      end

      response(422, "validation error") do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Only draft drives can be started" }
                   }
                 }
               }

        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :in_progress)
        end
        let(:uuid) { drive.uuid }
        let(:body) { { sales_person_accompanying: false } }

        run_test!
      end

      response(404, "drive not found") do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Drive not found" }
                   }
                 }
               }

        let(:uuid) { "non-existent-uuid" }
        let(:body) { { sales_person_accompanying: false } }

        run_test!
      end

      response(401, "unauthorized") do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Unauthorized" }
                   }
                 }
               }

        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle1,
                 customer: customer1,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :draft,
                 expected_return_datetime: 1.hour.from_now,
                 start_odometer_reading: 10000)
        end
        let(:uuid) { drive.uuid }
        let(:body) { { sales_person_accompanying: false } }
        let(:Authorization) { "Bearer invalid_token" }

        run_test!
      end
    end
  end
end
